import React, { useState } from 'react';
import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonButton,
  IonInput,
  IonItem,
  IonLabel,
  IonText,
  IonIcon,
  IonSpinner,
  IonAlert,
} from '@ionic/react';
import {
  logInOutline,
  personAddOutline,
  eyeOutline,
  eyeOffOutline,
  mailOutline,
  lockClosedOutline,
  personOutline,
  checkmarkCircleOutline,
  alertCircleOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { ROUTES } from '../../routes/routes';
import LoadingButton from '../../components/LoadingButton/LoadingButton';
import { debugLog } from '../../config/user-manager.config';
import { environmentConfig } from '../../config/environment.config';
import { Capacitor } from '@capacitor/core';
import { AuthService } from '../../services/auth.service';
import { DebugConfig } from '../../config/debug.config';
import { useUser } from '../../contexts/UserContext';
// NavigationDebug removed as part of simplification
import {
  validateEmailOrUsername,
  validatePasswordForOIDC,
  validateName,
  validatePasswordConfirmation
} from '../../utils/validation';
import './AuthPage.css';

interface FormErrors {
  email?: string;
  password?: string;
  name?: string;
  confirmPassword?: string;
}

interface FormData {
  email: string;
  password: string;
  name: string;
  confirmPassword: string;
}

const AuthPage: React.FC = () => {
  const history = useHistory();
  const auth = useAuth();
  const { updateCapacitorAuthState } = useUser();
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
    name: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<'success' | 'error'>('error');

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Validate email or username for Santillana Connect OIDC
    const emailOrUsernameResult = validateEmailOrUsername(formData.email);
    if (!emailOrUsernameResult.isValid) newErrors.email = emailOrUsernameResult.message;

    const passwordResult = validatePasswordForOIDC(formData.password);
    if (!passwordResult.isValid) newErrors.password = passwordResult.message;

    if (!isLogin) {
      const nameResult = validateName(formData.name);
      if (!nameResult.isValid) newErrors.name = nameResult.message;

      const confirmPasswordResult = validatePasswordConfirmation(formData.password, formData.confirmPassword);
      if (!confirmPasswordResult.isValid) newErrors.confirmPassword = confirmPasswordResult.message;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const simulateAuth = async (): Promise<{ success: boolean; message: string }> => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Simulate random success/failure for demo
    const success = Math.random() > 0.3; // 70% success rate

    return {
      success,
      message: success
        ? (isLogin ? 'Inicio de sesión exitoso' : 'Registro completado exitosamente')
        : (isLogin ? 'Credenciales incorrectas' : 'Error al crear la cuenta')
    };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    console.log('🚀 [UI] Starting OIDC authentication flow');
    debugLog('AuthPage - Starting OIDC authentication flow');

    // Log authentication flow start for debugging
    DebugConfig.logAuthFlowStart();

    setIsLoading(true);
    setShowAlert(false); // Clear any previous alerts

    try {
      // Check if running on native platform (Android/iOS)
      if (Capacitor.isNativePlatform()) {
        console.log('📱 [UI] Using modern Capacitor WebView authentication');
        console.log('📱 [UI] Platform:', Capacitor.getPlatform());
        console.log('📱 [UI] InAppBrowser available:', Capacitor.isPluginAvailable('InAppBrowser'));
        debugLog('AuthPage - Using modern Capacitor WebView authentication');

        // Use modern unified authentication service
        const authResult = await AuthService.signIn();

        console.log('🎉 [UI] Authentication successful:', {
          userId: authResult.profile.sub,
          userName: authResult.profile.name,
          userEmail: authResult.profile.email,
          expiresIn: authResult.expiresIn
        });

        debugLog('AuthPage - Authentication successful:', {
          userId: authResult.profile.sub,
          userName: authResult.profile.name,
          userEmail: authResult.profile.email
        });

        // Log authentication flow success
        DebugConfig.logAuthFlowEnd(true, {
          userId: authResult.profile.sub,
          userName: authResult.profile.name,
          userEmail: authResult.profile.email
        });

        // Update UserContext with Capacitor authentication state
        console.log('👤 [UI] Updating user context with authentication result');
        if (updateCapacitorAuthState) {
          await updateCapacitorAuthState(authResult);
          console.log('✅ [UI] User context updated successfully');
        }

        // Show success message
        setAlertType('success');
        setAlertMessage(`¡Bienvenido, ${authResult.profile.name || authResult.profile.email}!`);
        setShowAlert(true);

        console.log('🏠 [UI] Redirecting to home page in 2 seconds');

        // Redirect to home after successful authentication
        setTimeout(async () => {
          console.log('🏠 [UI] Starting navigation to home page');
          console.log('🔍 [UI] Navigation state before authentication');

          setIsLoading(false); // Ensure loading is stopped before navigation

          // Double-check authentication state before navigation
          console.log('🔍 [UI] Verifying authentication state before navigation');

          try {
            // Verify user context is updated
            const currentUser = await AuthService.getCurrentUser();
            console.log('🔍 [UI] Current user:', {
              hasUser: !!currentUser,
              userId: currentUser?.profile?.sub,
              userName: currentUser?.profile?.name
            });

            // Use enhanced navigation with multiple fallback methods
            console.log('🏠 [UI] Attempting enhanced navigation to:', ROUTES.HOME);
            history.push(ROUTES.HOME);

            // Verify navigation after a delay
            setTimeout(() => {
              console.log('🔍 [UI] Navigation state after authentication');

              if (window.location.pathname.includes('/auth')) {
                console.error('🚨 [UI] Navigation failed - still on auth page');
                console.log('🔄 [UI] Forcing page reload to home');
                window.location.href = ROUTES.HOME;
              } else {
                console.log('✅ [UI] Navigation successful!');
              }
            }, 2000);

          } catch (error) {
            console.error('❌ [UI] Error during navigation verification:', error);
            // Still attempt navigation even if verification fails
            history.push(ROUTES.HOME);
          }
        }, 2000);

      } else {
        console.log('🌐 [UI] Using web browser OIDC redirect flow');
        debugLog('AuthPage - Using web browser OIDC redirect flow');

        // Use standard web OIDC redirect for browsers
        await auth.signinRedirect();

        // Note: After this call, user will be redirected to Santillana Connect
        // and then back to our /callback route where CallbackPage will handle the response
      }
    } catch (error) {
      console.error('❌ [UI] Error during OIDC authentication:', error);
      console.error('AuthPage - Error during OIDC authentication:', error);

      // Log authentication flow failure
      DebugConfig.logAuthFlowEnd(false, { error: String(error) });

      // Use enhanced error handling
      const { AuthErrorHandler } = await import('../../utils/auth-error-handler');
      const authError = AuthErrorHandler.parseError(error);

      console.log('🔍 [UI] Authentication error details:', {
        code: authError.code,
        message: authError.message,
        userMessage: authError.userMessage,
        isRetryable: authError.isRetryable
      });

      debugLog('AuthPage - Parsed authentication error:', authError);

      setAlertType('error');
      setAlertMessage(authError.userMessage);
      setShowAlert(true);
      setIsLoading(false);

      // Log debug information for troubleshooting
      if (environmentConfig.debugAuth) {
        console.error('AuthPage - Debug info:', AuthErrorHandler.getDebugInfo(authError));
      }

      // Auto-hide error after 5 seconds if retryable
      if (authError.isRetryable) {
        setTimeout(() => {
          setShowAlert(false);
        }, 5000);
      }
    }
  };

  const handleToggleMode = () => {
    setIsLogin(!isLogin);
    setFormData({
      email: '',
      password: '',
      name: '',
      confirmPassword: ''
    });
    setErrors({});
  };

  const handleRunTests = async () => {
    if (!environmentConfig.debugAuth) return;

    try {
      console.log('🧪 [UI] Running basic authentication tests...');

      // Basic authentication test
      const isAuthenticated = await AuthService.isAuthenticated();
      const currentUser = await AuthService.getCurrentUser();

      const results = [
        { name: 'Authentication Check', passed: typeof isAuthenticated === 'boolean' },
        { name: 'User Retrieval', passed: currentUser !== undefined }
      ];

      const passedCount = results.filter(r => r.passed).length;
      console.log('🧪 [UI] Test results:', results);

      setAlertType(passedCount === results.length ? 'success' : 'error');
      setAlertMessage(`Tests completed: ${passedCount}/${results.length} passed. Check console for details.`);
      setShowAlert(true);
    } catch (error) {
      console.error('Test execution failed:', error);
      setAlertType('error');
      setAlertMessage('Test execution failed. Check console for details.');
      setShowAlert(true);
    }
  };



  return (
    <IonPage>
      <IonContent className="auth-content">
        {/* Main Auth Card */}
        <div className="auth-container">
          <IonCard className="auth-card">
            <IonCardContent className="auth-card-content">
              {/* Header */}
              <div className="auth-header">
                <div className="auth-icon-container">
                  <IonIcon
                    icon={isLogin ? logInOutline : personAddOutline}
                    className="auth-main-icon"
                    aria-hidden="true"
                  />
                </div>
                <h1 className="auth-title">
                  {isLogin ? 'Bienvenido de nuevo' : 'Crear cuenta'}
                </h1>
                <p className="auth-subtitle">
                  {isLogin
                    ? 'Ingresa tu usuario o correo electrónico. Serás redirigido a Santillana Connect para autenticarte.'
                    : 'Completa los datos para registrarte'
                  }
                </p>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="auth-form" noValidate>
                {/* Name Field (Registration only) */}
                {!isLogin && (
                  <div className="form-group">
                    <IonItem
                      className={`form-item ${errors.name ? 'form-item-error' : ''}`}
                      lines="none"
                    >
                      <IonIcon
                        icon={personOutline}
                        slot="start"
                        className="form-icon"
                        aria-hidden="true"
                      />
                      <IonLabel position="stacked">Nombre completo</IonLabel>
                      <IonInput
                        type="text"
                        value={formData.name}
                        onIonInput={e => handleInputChange('name', e.detail.value!)}
                        placeholder="Ingresa tu nombre"
                        className="form-input"
                        aria-describedby={errors.name ? 'name-error' : undefined}
                        aria-invalid={!!errors.name}
                      />
                    </IonItem>
                    {errors.name && (
                      <IonText color="danger" className="error-text" id="name-error">
                        <small>
                          <IonIcon icon={alertCircleOutline} className="error-icon" />
                          {errors.name}
                        </small>
                      </IonText>
                    )}
                  </div>
                )}

                {/* Email Field */}
                <div className="form-group">
                  <IonItem
                    className={`form-item ${errors.email ? 'form-item-error' : ''}`}
                    lines="none"
                  >
                    <IonIcon
                      icon={mailOutline}
                      slot="start"
                      className="form-icon"
                      aria-hidden="true"
                    />
                    <IonLabel position="stacked">Usuario o Correo electrónico</IonLabel>
                    <IonInput
                      type="text"
                      value={formData.email}
                      onIonInput={e => handleInputChange('email', e.detail.value!)}
                      placeholder="fami.<NAME_EMAIL>"
                      className="form-input"
                      aria-describedby={errors.email ? 'email-error' : undefined}
                      aria-invalid={!!errors.email}
                    />
                  </IonItem>
                  {errors.email && (
                    <IonText color="danger" className="error-text" id="email-error">
                      <small>
                        <IonIcon icon={alertCircleOutline} className="error-icon" />
                        {errors.email}
                      </small>
                    </IonText>
                  )}
                </div>

                {/* Password Field */}
                <div className="form-group">
                  <IonItem
                    className={`form-item ${errors.password ? 'form-item-error' : ''}`}
                    lines="none"
                  >
                    <IonIcon
                      icon={lockClosedOutline}
                      slot="start"
                      className="form-icon"
                      aria-hidden="true"
                    />
                    <IonLabel position="stacked">Contraseña</IonLabel>
                    <IonInput
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onIonInput={e => handleInputChange('password', e.detail.value!)}
                      placeholder="Ingresa tu contraseña"
                      className="form-input"
                      aria-describedby={errors.password ? 'password-error' : undefined}
                      aria-invalid={!!errors.password}
                    />
                    <IonButton
                      fill="clear"
                      slot="end"
                      onClick={() => setShowPassword(!showPassword)}
                      className="password-toggle"
                      aria-label={showPassword ? 'Ocultar contraseña' : 'Mostrar contraseña'}
                    >
                      <IonIcon icon={showPassword ? eyeOffOutline : eyeOutline} />
                    </IonButton>
                  </IonItem>
                  {errors.password && (
                    <IonText color="danger" className="error-text" id="password-error">
                      <small>
                        <IonIcon icon={alertCircleOutline} className="error-icon" />
                        {errors.password}
                      </small>
                    </IonText>
                  )}
                </div>

                {/* Confirm Password Field (Registration only) */}
                {!isLogin && (
                  <div className="form-group">
                    <IonItem
                      className={`form-item ${errors.confirmPassword ? 'form-item-error' : ''}`}
                      lines="none"
                    >
                      <IonIcon
                        icon={lockClosedOutline}
                        slot="start"
                        className="form-icon"
                        aria-hidden="true"
                      />
                      <IonLabel position="stacked">Confirmar contraseña</IonLabel>
                      <IonInput
                        type={showConfirmPassword ? 'text' : 'password'}
                        value={formData.confirmPassword}
                        onIonInput={e => handleInputChange('confirmPassword', e.detail.value!)}
                        placeholder="Confirma tu contraseña"
                        className="form-input"
                        aria-describedby={errors.confirmPassword ? 'confirm-password-error' : undefined}
                        aria-invalid={!!errors.confirmPassword}
                      />
                      <IonButton
                        fill="clear"
                        slot="end"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="password-toggle"
                        aria-label={showConfirmPassword ? 'Ocultar contraseña' : 'Mostrar contraseña'}
                      >
                        <IonIcon icon={showConfirmPassword ? eyeOffOutline : eyeOutline} />
                      </IonButton>
                    </IonItem>
                    {errors.confirmPassword && (
                      <IonText color="danger" className="error-text" id="confirm-password-error">
                        <small>
                          <IonIcon icon={alertCircleOutline} className="error-icon" />
                          {errors.confirmPassword}
                        </small>
                      </IonText>
                    )}
                  </div>
                )}

                {/* Submit Button */}
                <LoadingButton
                  expand="block"
                  type="submit"
                  className="auth-submit-button"
                  loading={isLoading}
                  loadingText={isLogin ? 'Iniciando sesión...' : 'Creando cuenta...'}
                  icon={isLogin ? logInOutline : personAddOutline}
                  iconSlot="start"
                  aria-label={isLogin ? 'Iniciar sesión' : 'Crear cuenta'}
                >
                  {isLogin ? 'Iniciar Sesión' : 'Crear Cuenta'}
                </LoadingButton>
              </form>

              {/* Toggle Mode Button */}
              <div className="auth-footer">
                <IonButton
                  fill="clear"
                  expand="block"
                  onClick={handleToggleMode}
                  className="toggle-mode-button"
                  disabled={isLoading}
                  aria-label={isLogin ? 'Cambiar a registro' : 'Cambiar a inicio de sesión'}
                >
                  {isLogin
                    ? '¿No tienes cuenta? Regístrate aquí'
                    : '¿Ya tienes cuenta? Inicia sesión'}
                </IonButton>
              </div>

              {/* Debug Test Button (only in debug mode) */}
              {environmentConfig.debugAuth && (
                <div className="mt-4">
                  <IonButton
                    fill="outline"
                    expand="block"
                    onClick={handleRunTests}
                    color="secondary"
                    size="small"
                  >
                    🧪 Run Authentication Tests
                  </IonButton>
                </div>
              )}
            </IonCardContent>
          </IonCard>
        </div>

        {/* Alert for success/error messages */}
        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header={alertType === 'success' ? 'Éxito' : 'Error'}
          message={alertMessage}
          buttons={['OK']}
          cssClass={`auth-alert ${alertType === 'success' ? 'alert-success' : 'alert-error'}`}
        />
      </IonContent>
    </IonPage>
  );
};

export default AuthPage; 