import React, { useEffect, useState } from "react";
import { IonRouterOutlet, IonTabs, IonTabBar, IonTabButton, IonIcon, IonLabel, IonPage, IonContent } from '@ionic/react';
import { home as homeIcon, logOut as logoutIcon, square, triangle } from 'ionicons/icons';
import { Route, Redirect, useHistory, useLocation } from "react-router-dom";
import RouteGuard from "./RouteGuard";
import { useAuth } from 'react-oidc-context';
import { AuthService } from "../services/auth.service";
import { debugLog, userManager } from "../config/user-manager.config";
import { environmentConfig } from "../config/environment.config";
// OIDC debug utilities removed as part of simplification
import { ROUTES } from './routes';
import WelcomeSlides from '../pages/WelcomeSlides/WelcomeSlides';
import AuthPage from '../pages/Auth/AuthPage';
import Tab1 from "../pages/HomePage/Tab1";
import Tab2 from "../pages/HomePage/Tab2";
import Tab3 from "../pages/HomePage/Tab3";
import CatalogoTab from "../pages/HomePage/CatalogoTab";
import AccountPage from "../pages/AccountPage/AccountPage";
import AuthErrorBoundary from '../components/ErrorBoundary/AuthErrorBoundary';
import AuthDebugPage from '../pages/Debug/AuthDebugPage';

// OIDC Callback page - handles return from Santillana Connect
const CallbackPage: React.FC = () => {
  const auth = useAuth();
  const history = useHistory();
  const location = useLocation();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      try {
        debugLog("CallbackPage - Starting OIDC callback processing");

        // Debug current state
        console.log('🔍 [CALLBACK] Processing OAuth callback');
        console.log('🔍 [CALLBACK] URL parameters:', location.search);

        // Check if we have the authorization code in the URL
        const urlParams = new URLSearchParams(location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');

        if (error) {
          debugLog("CallbackPage - OIDC error in URL parameters", { error, error_description: urlParams.get('error_description') });
          setError(`Error de autenticación: ${error}`);
          setIsProcessing(false);
          return;
        }

        if (!code) {
          debugLog("CallbackPage - No authorization code found in URL");
          setError("No se encontró el código de autorización en la respuesta");
          setIsProcessing(false);
          return;
        }

        if (!state) {
          debugLog("CallbackPage - No state parameter found in URL");
          setError("No se encontró el parámetro de estado en la respuesta");
          setIsProcessing(false);
          return;
        }

        debugLog("CallbackPage - Found authorization code and state, processing...", {
          hasCode: !!code,
          hasState: !!state,
          codeLength: code?.length,
          stateLength: state?.length
        });

        // Use UserManager directly to handle the callback
        // This should properly match the state and exchange the code for tokens
        const user = await userManager.signinRedirectCallback();

        debugLog("CallbackPage - OIDC callback processed successfully", {
          userId: user.profile?.sub,
          userName: user.profile?.name,
          userEmail: user.profile?.email,
          hasAccessToken: !!user.access_token,
          hasRefreshToken: !!user.refresh_token
        });

        // Store tokens for native apps
        await AuthStorageService.storeTokens(user);

        // Small delay to ensure everything is processed
        setTimeout(() => {
          debugLog("CallbackPage - Redirecting to home page");
          history.replace(ROUTES.HOME);
        }, 500);

      } catch (error: any) {
        console.error("CallbackPage - Error processing OIDC callback:", error);
        debugLog("CallbackPage - OIDC callback error", {
          errorMessage: error.message,
          errorName: error.name,
          errorStack: error.stack
        });

        // Use enhanced error handling
        const { AuthErrorHandler } = await import('../utils/auth-error-handler');
        const authError = AuthErrorHandler.parseError(error);

        debugLog("CallbackPage - Parsed authentication error:", authError);

        // Provide specific error messages
        setError(authError.userMessage);

        // Log debug information for troubleshooting
        if (environmentConfig.debugAuth) {
          console.error("CallbackPage - Debug info:", AuthErrorHandler.getDebugInfo(authError));

          // For state mismatch errors, log debug info
          if (authError.code === 'STATE_MISMATCH') {
            console.log('🔍 [CALLBACK] State mismatch error - checking storage');
          }
        }

        setIsProcessing(false);
      }
    };

    // Only process if we haven't started processing yet
    if (isProcessing && !error) {
      processCallback();
    }
  }, [location, history, isProcessing, error]);

  if (error) {
    return (
      <IonPage>
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="text-red-500 mb-4">
              <svg className="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold mb-2">Error de Autenticación</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="space-y-2">
              <button
                onClick={() => {
                  debugLog("CallbackPage - Retry authentication");
                  history.replace(ROUTES.AUTH);
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors mr-2"
              >
                Intentar de nuevo
              </button>
              {error?.includes("estado") && (
                <button
                  onClick={() => {
                    debugLog("CallbackPage - Clearing storage and retrying");
                    // Clear authentication storage
                    AuthService.signOut();
                    history.replace(ROUTES.AUTH);
                  }}
                  className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Limpiar datos y reintentar
                </button>
              )}
            </div>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonContent className="ion-padding">
        <div className="flex flex-col items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-lg mb-2">Procesando inicio de sesión...</p>
          <p className="text-sm text-gray-600 mb-4">Validando credenciales con Santillana Connect...</p>

          {/* Emergency redirect button - only show after some time */}
          <button
            onClick={() => {
              debugLog("CallbackPage - Manual redirect triggered");
              history.replace(ROUTES.AUTH);
            }}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            style={{ display: isProcessing ? 'none' : 'block' }}
          >
            Volver al login
          </button>
        </div>
      </IonContent>
    </IonPage>
  );
};

// Silent refresh page for token renewal
const SilentRefreshPage: React.FC = () => {
  useEffect(() => {
    debugLog("SilentRefreshPage - Processing silent refresh");
  }, []);

  return (
    <IonPage>
      <IonContent>
        <div style={{ display: 'none' }}>
          Renovando tokens de forma silenciosa...
        </div>
      </IonContent>
    </IonPage>
  );
};

// Logout page for handling post-logout redirect
const LogoutPage: React.FC = () => {
  const auth = useAuth();
  const [isProcessed, setIsProcessed] = useState(false);

  useEffect(() => {
    if (isProcessed) return;

    const handleLogout = async () => {
      try {
        setIsProcessed(true);
        debugLog("LogoutPage - Processing logout...");

        await auth.removeUser();
        await AuthStorageService.clearTokens();

        debugLog("LogoutPage - Logout completed, tokens cleared");

        setTimeout(() => {
          auth.signinRedirect().catch(console.error);
        }, 2000);

      } catch (error) {
        console.error("❌ LogoutPage - Error during logout:", error);
        setTimeout(() => {
          auth.signinRedirect().catch(console.error);
        }, 2000);
      }
    };

    handleLogout();
  }, [auth, isProcessed]);

  return (
    <IonPage>
      <IonContent className="ion-padding">
        <div className="flex flex-col items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mb-4"></div>
          <p className="text-lg mb-2">Cerrando sesión...</p>
          <p className="text-sm text-gray-600">Serás redirigido al login automáticamente</p>
        </div>
      </IonContent>
    </IonPage>
  );
};

const AppRoutes: React.FC = () => {
  return (
    <IonRouterOutlet>
      {/* Rutas públicas */}
      <Route exact path={ROUTES.WELCOME} component={WelcomeSlides} />

      {/* Authentication routes wrapped with error boundary */}
      <Route exact path={ROUTES.AUTH}>
        <AuthErrorBoundary>
          <AuthPage />
        </AuthErrorBoundary>
      </Route>

      {/** OIDC callback route (after login) */}
      <Route exact path="/callback">
        <AuthErrorBoundary>
          <CallbackPage />
        </AuthErrorBoundary>
      </Route>

      {/** Silent refresh route (for token renewal) */}
      <Route exact path="/silent-refresh" component={SilentRefreshPage} />

      {/** Post-logout redirect route */}
      <Route exact path="/logout" component={LogoutPage} />

      {/** Debug route for testing authentication (optional - only for development) */}
      {process.env.NODE_ENV === 'development' && (
        <Route exact path="/debug" component={AuthDebugPage} />
      )}

      {/* Protected routes - all require OIDC authentication */}
      <Route path={ROUTES.HOME}>
        <RouteGuard>
          <IonTabs>
            <IonRouterOutlet>
              {/* Tab sub-routes */}
              <Route exact path={ROUTES.TABS.HOME} component={Tab1} />
              <Route exact path={ROUTES.TABS.REPORTS} component={Tab2} />
              <Route exact path={ROUTES.TABS.CONNECTION} component={Tab3} />
              <Route exact path={ROUTES.TABS.RESOURCES} component={Tab3} />
              <Route exact path={ROUTES.TABS.CATALOG} component={CatalogoTab} />
              <Route exact path={ROUTES.TABS.ACCOUNT} component={AccountPage} />

              {/* Default tab redirect */}
              <Route exact path={ROUTES.HOME}>
                <Redirect to={ROUTES.TABS.HOME} />
              </Route>
            </IonRouterOutlet>

            {/* Tab Bar */}
            <IonTabBar slot="bottom">
              <IonTabButton tab="inicio" href={ROUTES.TABS.HOME}>
                <IonIcon icon={homeIcon} />
                <IonLabel>Inicio</IonLabel>
              </IonTabButton>

              <IonTabButton tab="informes" href={ROUTES.TABS.REPORTS}>
                <IonIcon icon={square} />
                <IonLabel>Informes</IonLabel>
              </IonTabButton>

              <IonTabButton tab="conexion" href={ROUTES.TABS.CONNECTION}>
                <IonIcon icon={triangle} />
                <IonLabel>Conexión</IonLabel>
              </IonTabButton>

              <IonTabButton tab="recursos" href={ROUTES.TABS.RESOURCES}>
                <IonIcon icon={triangle} />
                <IonLabel>Recursos</IonLabel>
              </IonTabButton>

              <IonTabButton tab="catalogo" href={ROUTES.TABS.CATALOG}>
                <IonIcon icon={square} />
                <IonLabel>Catálogo</IonLabel>
              </IonTabButton>

              <IonTabButton tab="cuenta" href={ROUTES.TABS.ACCOUNT}>
                <IonIcon icon={homeIcon} />
                <IonLabel>Cuenta</IonLabel>
              </IonTabButton>
            </IonTabBar>
          </IonTabs>
        </RouteGuard>
      </Route>

      {/* Ruta por defecto */}
      <Route exact path="/">
        <Redirect to={ROUTES.HOME} />
      </Route>
    </IonRouterOutlet>
  );
};

export default AppRoutes;
