/**
 * Unified Authentication Service
 * Consolidates all authentication functionality for Santillana Connect OAuth2
 * Supports both web and mobile platforms with InAppBrowser integration
 */

import { Capacitor, CapacitorCookies } from '@capacitor/core';
import { InAppBrowser, DefaultSystemBrowserOptions } from '@capacitor/inappbrowser';
import { App } from '@capacitor/app';
import { Preferences } from '@capacitor/preferences';
import { User } from 'oidc-client-ts';
import { environmentConfig, debugLog } from '../config/environment.config';
import { CryptoUtils } from '../utils/crypto';
import { HttpService } from '../utils/http';

// Types and Interfaces
export interface AuthTokens {
  access_token: string;
  id_token?: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
}

export interface UserProfile {
  sub: string;
  name: string;
  email: string;
  picture?: string;
  [key: string]: any;
}

export interface AuthResult {
  accessToken: string;
  idToken?: string;
  refreshToken?: string;
  expiresIn: number;
  profile: UserProfile;
}

interface AuthState {
  state: string;
  codeVerifier: string;
  nonce: string;
  redirectUri: string;
  timestamp: number;
}

interface TokenResponse {
  access_token: string;
  id_token: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
}

/**
 * Authentication Logger for consistent logging
 */
class AuthLogger {
  static log(message: string, data?: any) {
    console.log(`🔐 [AUTH] ${message}`, data || '');
    debugLog(`AUTH: ${message}`, data);
  }

  static error(message: string, error?: any) {
    console.error(`❌ [AUTH] ${message}`, error || '');
    debugLog(`AUTH ERROR: ${message}`, error);
  }
}

/**
 * Unified Authentication Service
 */
export class AuthService {
  private static readonly TOKEN_KEYS = {
    ACCESS_TOKEN: "oidc.access_token",
    ID_TOKEN: "oidc.id_token", 
    REFRESH_TOKEN: "oidc.refresh_token",
    USER_DATA: "oidc.user_data",
    EXPIRES_AT: "oidc.expires_at",
    AUTH_TOKENS: "auth_tokens"
  } as const;

  private static isNative = Capacitor.getPlatform() !== "web";
  private static authInProgress = false;

  /**
   * Initialize the authentication service
   */
  static async initialize(): Promise<void> {
    try {
      AuthLogger.log('Initializing authentication service');
      
      if (this.isNative) {
        // Set up deep link handling for mobile platforms
        App.addListener('appUrlOpen', (event) => {
          this.handleDeepLink(event.url);
        });
        
        AuthLogger.log('Deep link handling configured for native platform');
      }
      
      AuthLogger.log('Authentication service initialized successfully');
    } catch (error) {
      AuthLogger.error('Failed to initialize authentication service', error);
      throw error;
    }
  }

  /**
   * Start OAuth2 authentication flow
   * Smart authentication - checks for existing valid session first
   */
  static async signIn(): Promise<AuthResult> {
    try {
      AuthLogger.log('Starting smart authentication flow');

      // Check for existing valid authentication
      const existingAuth = await this.getCurrentUser();
      if (existingAuth) {
        AuthLogger.log('Using existing valid authentication');
        return existingAuth;
      }

      return await this.signInForced();
    } catch (error) {
      AuthLogger.error('Smart authentication failed', error);
      throw error;
    }
  }

  /**
   * Force fresh authentication (always shows login screen)
   */
  static async signInForced(): Promise<AuthResult> {
    if (this.authInProgress) {
      throw new Error('Authentication already in progress');
    }

    try {
      this.authInProgress = true;
      AuthLogger.log('Starting forced authentication flow');

      // Clear browser session to ensure fresh authentication
      await this.clearBrowserSession();

      // Generate PKCE parameters and state
      const authState = await this.generateAuthState();
      await this.storeAuthState(authState);

      const authUrl = await this.buildAuthUrl(authState);
      AuthLogger.log('Opening authentication URL', { authUrl });

      if (this.isNative) {
        return await this.authenticateNative(authUrl, authState);
      } else {
        return await this.authenticateWeb(authUrl, authState);
      }
    } catch (error) {
      AuthLogger.error('Forced authentication failed', error);
      throw error;
    } finally {
      this.authInProgress = false;
    }
  }

  /**
   * Native authentication using InAppBrowser
   */
  private static async authenticateNative(authUrl: string, authState: AuthState): Promise<AuthResult> {
    return new Promise((resolve, reject) => {
      const browserOptions: DefaultSystemBrowserOptions = {
        url: authUrl,
        presentationStyle: 'popover',
        showTitle: false,
        toolbarColor: '#ffffff'
      };

      InAppBrowser.openInSystemBrowser(browserOptions).catch((error) => {
        AuthLogger.error('Failed to open system browser', error);
        reject(new Error('Failed to open authentication browser'));
      });

      // Set up deep link listener
      const removeListener = App.addListener('appUrlOpen', async (event) => {
        try {
          removeListener.remove();
          const result = await this.handleAuthCallback(event.url, authState);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      // Timeout after 5 minutes
      setTimeout(() => {
        removeListener.remove();
        reject(new Error('Authentication timeout'));
      }, 300000);
    });
  }

  /**
   * Web authentication using redirect
   */
  private static async authenticateWeb(authUrl: string, authState: AuthState): Promise<AuthResult> {
    // For web, we redirect to the auth URL
    window.location.href = authUrl;
    
    // This will never resolve as the page redirects
    return new Promise(() => {});
  }

  /**
   * Handle authentication callback
   */
  private static async handleAuthCallback(callbackUrl: string, authState: AuthState): Promise<AuthResult> {
    try {
      AuthLogger.log('Processing authentication callback', { callbackUrl });

      const url = new URL(callbackUrl);
      const code = url.searchParams.get('code');
      const state = url.searchParams.get('state');
      const error = url.searchParams.get('error');

      if (error) {
        throw new Error(`Authentication error: ${error}`);
      }

      if (!code || !state) {
        throw new Error('Missing authorization code or state parameter');
      }

      // Verify state parameter
      if (state !== authState.state) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      // Exchange authorization code for tokens
      const tokenResponse = await this.exchangeCodeForTokens(code, authState);

      // Parse ID token to get user profile
      const profile = this.parseJwtPayload(tokenResponse.id_token);

      // Create auth result
      const authResult: AuthResult = {
        accessToken: tokenResponse.access_token,
        idToken: tokenResponse.id_token,
        refreshToken: tokenResponse.refresh_token,
        expiresIn: tokenResponse.expires_in || 3600,
        profile: {
          sub: profile.sub,
          name: profile.name,
          email: profile.email,
          ...profile
        }
      };

      // Store authentication data
      await this.storeAuthResult(authResult);

      AuthLogger.log('Authentication successful', { 
        userId: profile.sub,
        userName: profile.name 
      });

      return authResult;
    } catch (error) {
      AuthLogger.error('Callback handling failed', error);
      throw error;
    }
  }

  /**
   * Generate authentication state with PKCE parameters
   */
  private static async generateAuthState(): Promise<AuthState> {
    try {
      const state = this.generateRandomString(32);
      const codeVerifier = this.generateRandomString(128);
      const nonce = this.generateRandomString(32);

      return {
        state,
        codeVerifier,
        nonce,
        redirectUri: environmentConfig.redirectUris?.callback || `${environmentConfig.nativeBaseUrl}/callback`,
        timestamp: Date.now()
      };
    } catch (error) {
      AuthLogger.error('Failed to generate authentication state', error);
      throw new Error(`Authentication state generation failed: ${error}`);
    }
  }

  /**
   * Build OAuth2 authorization URL
   */
  private static async buildAuthUrl(authState: AuthState): Promise<string> {
    const hash = await CryptoUtils.sha256(authState.codeVerifier);
    const codeChallenge = CryptoUtils.base64URLEncode(hash);

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: environmentConfig.clientId,
      redirect_uri: authState.redirectUri,
      scope: environmentConfig.scope,
      state: authState.state,
      nonce: authState.nonce,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256'
    });

    return `${environmentConfig.authority}/connect/authorize?${params.toString()}`;
  }

  /**
   * Exchange authorization code for tokens
   */
  private static async exchangeCodeForTokens(code: string, authState: AuthState): Promise<TokenResponse> {
    try {
      const tokenEndpoint = `${environmentConfig.authority}/connect/token`;
      
      const body = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: environmentConfig.clientId,
        code: code,
        redirect_uri: authState.redirectUri,
        code_verifier: authState.codeVerifier
      });

      const response = await HttpService.post(tokenEndpoint, body.toString(), {
        'Content-Type': 'application/x-www-form-urlencoded'
      });

      if (!response.ok) {
        throw new Error(`Token exchange failed: ${response.status}`);
      }

      const tokenData = response.data;
      
      AuthLogger.log('Token exchange successful');
      return tokenData;
    } catch (error) {
      AuthLogger.error('Token exchange failed', error);
      throw error;
    }
  }

  /**
   * Parse JWT payload without verification (for client-side use only)
   */
  private static parseJwtPayload(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = parts[1];
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
      return JSON.parse(decoded);
    } catch (error) {
      AuthLogger.error('JWT parsing failed', error);
      throw new Error('Failed to parse JWT token');
    }
  }

  /**
   * Store authentication result securely
   */
  private static async storeAuthResult(authResult: AuthResult): Promise<void> {
    try {
      const expiresAt = Date.now() + (authResult.expiresIn * 1000);
      
      const authData = {
        accessToken: authResult.accessToken,
        idToken: authResult.idToken,
        refreshToken: authResult.refreshToken,
        expiresAt,
        profile: authResult.profile
      };

      if (this.isNative) {
        await Preferences.set({
          key: this.TOKEN_KEYS.AUTH_TOKENS,
          value: JSON.stringify(authData)
        });
      } else {
        localStorage.setItem(this.TOKEN_KEYS.AUTH_TOKENS, JSON.stringify(authData));
      }

      AuthLogger.log('Authentication data stored successfully');
    } catch (error) {
      AuthLogger.error('Failed to store authentication data', error);
      throw error;
    }
  }

  /**
   * Get current authenticated user
   */
  static async getCurrentUser(): Promise<AuthResult | null> {
    try {
      let storedData: string | null = null;

      if (this.isNative) {
        const result = await Preferences.get({ key: this.TOKEN_KEYS.AUTH_TOKENS });
        storedData = result.value;
      } else {
        storedData = localStorage.getItem(this.TOKEN_KEYS.AUTH_TOKENS);
      }

      if (!storedData) {
        return null;
      }

      const authData = JSON.parse(storedData);

      // Check if token is expired
      if (Date.now() >= authData.expiresAt) {
        AuthLogger.log('Stored token is expired');
        await this.signOut();
        return null;
      }

      return {
        accessToken: authData.accessToken,
        idToken: authData.idToken,
        refreshToken: authData.refreshToken,
        expiresIn: Math.floor((authData.expiresAt - Date.now()) / 1000),
        profile: authData.profile
      };
    } catch (error) {
      AuthLogger.error('Error getting current user', error);
      return null;
    }
  }

  /**
   * Sign out user and clear all authentication data
   */
  static async signOut(): Promise<void> {
    try {
      AuthLogger.log('Starting sign out');
      
      await this.clearAuthData();
      await this.clearBrowserSession();
      
      // For web, redirect to logout endpoint
      if (!this.isNative) {
        const logoutUrl = `${environmentConfig.authority}/connect/endsession?post_logout_redirect_uri=${encodeURIComponent(window.location.origin)}`;
        window.location.href = logoutUrl;
      }

      AuthLogger.log('Sign out completed');
    } catch (error) {
      AuthLogger.error('Sign out failed', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  static async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user !== null;
  }

  // Helper methods
  private static generateRandomString(length: number): string {
    return CryptoUtils.generateCodeVerifier(length);
  }

  private static async storeAuthState(authState: AuthState): Promise<void> {
    try {
      await Preferences.set({
        key: `auth_state_${authState.state}`,
        value: JSON.stringify(authState)
      });
    } catch (error) {
      AuthLogger.error('Error storing auth state', error);
      throw new Error('Failed to store authentication state');
    }
  }

  private static async clearAuthData(): Promise<void> {
    try {
      if (this.isNative) {
        await Promise.all([
          Preferences.remove({ key: this.TOKEN_KEYS.AUTH_TOKENS }),
          Preferences.remove({ key: this.TOKEN_KEYS.ACCESS_TOKEN }),
          Preferences.remove({ key: this.TOKEN_KEYS.ID_TOKEN }),
          Preferences.remove({ key: this.TOKEN_KEYS.REFRESH_TOKEN }),
          Preferences.remove({ key: this.TOKEN_KEYS.USER_DATA }),
          Preferences.remove({ key: this.TOKEN_KEYS.EXPIRES_AT })
        ]);
      } else {
        Object.values(this.TOKEN_KEYS).forEach(key => {
          localStorage.removeItem(key);
        });
      }
    } catch (error) {
      AuthLogger.error('Error clearing auth data', error);
    }
  }

  private static async clearBrowserSession(): Promise<void> {
    try {
      if (this.isNative) {
        await CapacitorCookies.clearAllCookies();
      }
    } catch (error) {
      AuthLogger.error('Error clearing browser session', error);
    }
  }

  private static async handleDeepLink(url: string): Promise<void> {
    try {
      if (url.includes('/callback')) {
        AuthLogger.log('Deep link callback received', { url });
        // Deep link handling is managed by the authentication flow
      }
    } catch (error) {
      AuthLogger.error('Deep link handling failed', error);
    }
  }
}
